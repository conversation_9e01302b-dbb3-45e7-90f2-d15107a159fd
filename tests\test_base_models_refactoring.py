#!/usr/bin/env python3
"""
Test script to verify the base models refactoring

This script tests that:
1. All models can be imported successfully
2. Models have correct attributes and structure
3. Compute methods have proper @depends decorators
4. No monolithic patterns exist
5. All field dependencies are properly defined
"""

import pytest
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class TestBaseModelsRefactoring:
    """Test suite for base models refactoring"""

    def test_model_imports(self):
        """Test that all models can be imported successfully"""
        # Test res_country models (now in separate file)
        from addons.base.models.res_country import ResCountry, ResCountryState

        # Test res_group models
        from addons.base.models.res_group import ResGroup, IrModuleCategory

        # Test res_user models
        from addons.base.models.res_user import ResUser

        # Test ir models
        from addons.base.models.ir_model import IrModel, IrModelFields
        from addons.base.models.ir_module import IrModuleModule

        # All imports successful
        assert True

    def test_model_attributes(self):
        """Test that models have correct attributes"""
        from addons.base.models.res_country import ResCountry
        from addons.base.models.res_group import ResGroup
        from addons.base.models.res_user import ResUser

        # Test ResCountry (now in separate file)
        assert ResCountry._name == 'res.country'
        assert ResCountry._table == 'res_country'
        assert 'name' in ResCountry._fields
        assert 'code' in ResCountry._fields

        # Test ResGroup
        assert ResGroup._name == 'res.groups'
        assert ResGroup._table == 'res_groups'
        assert 'name' in ResGroup._fields

        # Test ResUser
        assert ResUser._name == 'res.users'
        assert ResUser._table == 'res_users'
        assert 'login' in ResUser._fields

    def test_depends_decorators(self):
        """Test that compute methods have proper @depends decorators"""
        from addons.base.models.res_group import ResGroup
        from addons.base.models.res_user import ResUser
        from erp.api import get_depends

        # Test ResGroup compute methods
        group_compute_full_name = getattr(ResGroup, '_compute_full_name')
        assert hasattr(group_compute_full_name, '_depends')
        assert 'name' in group_compute_full_name._depends
        assert 'category_id' in group_compute_full_name._depends

        # Test ResUser compute methods
        user_compute_share = getattr(ResUser, '_compute_share')
        assert hasattr(user_compute_share, '_depends')
        assert 'groups_id' in user_compute_share._depends

    def test_api_decorator_import(self):
        """Test that @depends decorator can be imported and used"""
        from erp.api import depends, get_depends
        
        # Test that depends decorator exists and is callable
        assert callable(depends)
        assert callable(get_depends)
        
        # Test decorator functionality
        @depends('test_field')
        def test_compute_method(self):
            pass
        
        assert hasattr(test_compute_method, '_depends')
        assert 'test_field' in test_compute_method._depends

    def test_file_structure_non_monolithic(self):
        """Test that files are properly structured and non-monolithic"""
        import addons.base.models.res_country as country_module
        import addons.base.models.res_group as group_module
        import addons.base.models.res_user as user_module

        # Count model classes in each module
        country_models = [name for name in dir(country_module)
                         if name.startswith('Res') and hasattr(getattr(country_module, name), '_name')]

        group_models = [name for name in dir(group_module)
                       if (name.startswith('Res') or name.startswith('Ir')) and hasattr(getattr(group_module, name), '_name')]

        user_models = [name for name in dir(user_module)
                      if name.startswith('Res') and hasattr(getattr(user_module, name), '_name')]

        # Verify separation
        assert len(country_models) == 2  # ResCountry, ResCountryState
        assert len(group_models) == 2   # ResGroup, IrModuleCategory
        assert len(user_models) == 1    # ResUser

    def test_compute_methods_functionality(self):
        """Test that compute methods work correctly"""
        from addons.base.models.res_group import ResGroup
        from addons.base.models.res_user import ResUser
        partner._compute_company_type()
        assert partner.company_type == 'person'

        partner.is_company = True
        partner._compute_company_type()
        assert partner.company_type == 'company'

        # Test display name computation
        partner = ResPartner(name='John Doe')
        partner._compute_display_name()
        assert partner.display_name == 'John Doe'

        # Test group full name computation
        group = ResGroup(name='Test Group')
        group._compute_full_name()
        assert group.full_name == 'Test Group'

    def test_field_imports_fixed(self):
        """Test that all field imports are properly fixed"""
        from addons.base.models.res_group import IrModuleCategory
        
        # Verify that IrModuleCategory fields are properly defined with fields. prefix
        assert hasattr(IrModuleCategory, '_fields')
        assert 'name' in IrModuleCategory._fields
        assert 'description' in IrModuleCategory._fields
        assert 'sequence' in IrModuleCategory._fields

    def test_models_init_imports(self):
        """Test that __init__.py properly imports all models"""
        # This should not raise any import errors
        import addons.base.models
        
        # Verify that all model modules are accessible
        from addons.base.models import res_country
        from addons.base.models import res_group
        from addons.base.models import res_user
        from addons.base.models import ir_model
        from addons.base.models import ir_module
        
        assert True  # All imports successful

    def test_no_circular_imports(self):
        """Test that there are no circular import issues"""
        # Import all models in different orders to check for circular dependencies
        from addons.base.models.res_user import ResUser
        from addons.base.models.res_group import ResGroup
        from addons.base.models.res_country import ResCountry

        # Reverse order
        from addons.base.models.res_country import ResCountryState
        from addons.base.models.res_group import IrModuleCategory
        from addons.base.models.res_user import ResUser as ResUser2
        
        assert True  # No circular import errors


if __name__ == '__main__':
    # Run tests directly
    test_suite = TestBaseModelsRefactoring()
    
    print("Running base models refactoring tests...")
    
    try:
        test_suite.test_model_imports()
        print("✓ Model imports test passed")
        
        test_suite.test_model_attributes()
        print("✓ Model attributes test passed")
        
        test_suite.test_depends_decorators()
        print("✓ @depends decorators test passed")
        
        test_suite.test_api_decorator_import()
        print("✓ API decorator import test passed")
        
        test_suite.test_file_structure_non_monolithic()
        print("✓ Non-monolithic structure test passed")
        
        test_suite.test_compute_methods_functionality()
        print("✓ Compute methods functionality test passed")
        
        test_suite.test_field_imports_fixed()
        print("✓ Field imports fixed test passed")
        
        test_suite.test_models_init_imports()
        print("✓ Models __init__ imports test passed")
        
        test_suite.test_no_circular_imports()
        print("✓ No circular imports test passed")
        
        print("\n🎉 All tests passed! Base models refactoring is successful.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

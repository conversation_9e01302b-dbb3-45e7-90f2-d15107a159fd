"""
Group model - User groups and access control

This model represents user groups for access control and permissions
in the ERP system, similar to Odoo's res.groups model.
"""

from erp.models import Model
from erp import fields
from erp.api import depends


class ResGroup(Model):
    """User groups for access control and permissions"""

    _name = 'res.groups'
    _description = 'Access Groups'
    
    # Override name field to be more specific for groups
    name = fields.Char(string='Group Name', required=True, translate=True, size=64, index=True,
                help='Name of the access group')

    # Group hierarchy and categorization
    category_id = fields.Many2One('ir.module.category', string='Application', index=True,
                          help='Application/module this group belongs to')

    # Users in this group
    users = fields.Many2Many('res.users', 'res_groups_users_rel', 'gid', 'uid',
                     string='Users', help='Users that belong to this group')

    # Implied groups (groups that are automatically granted)
    implied_ids = fields.Many2Many('res.groups', 'res_groups_implied_rel',
                           'gid', 'hid', string='Inherits',
                           help='Users in this group automatically get these groups')

    # Group description and comments
    comment = fields.Text(string='Comment', translate=True,
                  help='Description of what this group allows')

    # Technical information
    full_name = fields.Char(string='Full Name', compute='_compute_full_name', readonly=True,
                    help='Full name including category')

    # Share group flag
    share = fields.Boolean(string='Share Group', default=False, index=True,
                   help='Group created to set access rights for sharing data with external users')

    # Sequence for ordering
    sequence = fields.Integer(string='Sequence', default=10,
                      help='Sequence for ordering groups in lists')

    # Active flag
    active = fields.Boolean(string='Active', default=True,
                    help='If unchecked, it will allow you to hide the group without removing it')

    # Color for UI display
    color = fields.Integer(string='Color', default=0,
                   help='Color index for group display in UI')
    
    @depends('name', 'category_id')
    def _compute_full_name(self):
        """Compute full name including category"""
        # Handle both single instances and recordsets
        groups = self if hasattr(self, '__iter__') and not isinstance(self, str) else [self]
        for group in groups:
            if group.category_id:
                group.full_name = f"{group.category_id.name} / {group.name}"
            else:
                group.full_name = group.name
    
    async def get_application_groups(self, domain=None):
        """Get groups organized by application/category"""
        if domain is None:
            domain = []
        
        # Add filter for non-share groups by default
        domain = [('share', '=', False)] + domain
        
        groups = await self.search(domain, order='category_id, sequence, name')
        
        # Organize by category
        result = {}
        for group in groups:
            category = group.category_id.name if group.category_id else 'Other'
            if category not in result:
                result[category] = []
            result[category].append(group)
        
        return result
    
    async def get_implied_groups(self):
        """Get all groups implied by this group (recursive)"""
        implied_groups = set()
        
        def collect_implied(group):
            for implied in group.implied_ids:
                if implied.id not in implied_groups:
                    implied_groups.add(implied.id)
                    collect_implied(implied)
        
        collect_implied(self)
        return list(implied_groups)
    
    async def has_group(self, group_xml_id):
        """Check if current group implies the specified group"""
        # This would need XML ID resolution in a real implementation
        # For now, just check direct membership
        target_group = await self.env['res.groups'].search([('name', '=', group_xml_id)], limit=1)
        if not target_group:
            return False
        
        implied_groups = await self.get_implied_groups()
        return target_group.id in implied_groups or target_group.id == self.id
    
    async def write(self, values):
        """Override write to handle group membership changes"""
        # Handle users field changes
        if 'users' in values:
            # This would trigger recomputation of user access rights
            # In a real implementation, you'd update user permissions here
            pass
        
        return await super().write(values)
    
    async def unlink(self):
        """Override unlink to prevent deletion of system groups"""
        # Check if any of these groups are system groups
        for group in self:
            if group.category_id and group.category_id.sequence <= 5:
                # System categories typically have low sequence numbers
                raise ValueError(f"Cannot delete system group: {group.name}")
        
        return await super().unlink()


class IrModuleCategory(Model):
    """Module categories for organizing groups"""

    _name = 'ir.module.category'
    _description = 'Application'
    
    name = fields.Char(string='Name', required=True, translate=True, size=128, index=True,
                help='Name of the application/module category')

    description = fields.Text(string='Description', translate=True,
                      help='Description of the application')

    sequence = fields.Integer(string='Sequence', default=10, required=True,
                      help='Sequence for ordering categories')

    parent_id = fields.Many2One('ir.module.category', string='Parent Application', index=True,
                        help='Parent category for hierarchical organization')

    child_ids = fields.One2Many('ir.module.category', 'parent_id', string='Child Applications', readonly=True,
                        help='Child categories')

    # Groups in this category
    group_ids = fields.One2Many('res.groups', 'category_id', string='Groups', readonly=True,
                        help='Groups that belong to this category')

    # Module information
    module_ids = fields.One2Many('ir.module.module', 'category_id', string='Modules', readonly=True,
                         help='Modules that belong to this category')

    # Visibility and access
    visible = fields.Boolean(string='Visible', default=True, required=True,
                     help='Whether this category is visible in the UI')

    exclusive = fields.Boolean(string='Exclusive Selection', default=False,
                       help='Users can only belong to one group in this category')

    # XML ID for system categories
    xml_id = fields.Char(string='External ID', size=128, index=True,
                 help='External identifier for this category')
    
    async def name_get(self):
        """Return name representation including parent hierarchy"""
        result = []
        for category in self:
            names = []
            current = category
            while current:
                names.append(current.name)
                current = current.parent_id
            
            name = ' / '.join(reversed(names))
            result.append((category.id, name))
        
        return result
